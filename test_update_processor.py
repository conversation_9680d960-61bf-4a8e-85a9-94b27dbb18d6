"""
测试UpdateTimeProcessor的功能
"""
from datetime import datetime
from wmb_update_process import UpdateTimeProcessor


def test_time_logic():
    """测试时间判断逻辑"""
    print("=" * 50)
    print("测试时间判断逻辑")
    print("=" * 50)
    
    processor = UpdateTimeProcessor()
    
    # 测试不同时间点
    test_times = [
        datetime(2025, 9, 3, 8, 30),   # 8:30 - 应该取昨天
        datetime(2025, 9, 3, 11, 59),  # 11:59 - 应该取昨天
        datetime(2025, 9, 3, 12, 0),   # 12:00 - 应该取当天
        datetime(2025, 9, 3, 15, 30),  # 15:30 - 应该取当天
        datetime(2025, 9, 3, 23, 59),  # 23:59 - 应该取当天
    ]
    
    for test_time in test_times:
        processor.current_time = test_time
        target_date = processor.get_target_date()
        expected = "昨天" if test_time.hour < 12 else "当天"
        print(f"测试时间: {test_time.strftime('%H:%M:%S')} -> 目标日期: {target_date} ({expected})")


def test_data_count():
    """测试数据统计功能"""
    print("\n" + "=" * 50)
    print("测试数据统计功能")
    print("=" * 50)
    
    try:
        processor = UpdateTimeProcessor()
        target_date = processor.get_target_date()
        
        # 测试公司数据统计
        company_count = processor.get_update_data_count(processor.table_source_company, target_date)
        print(f"公司表更新数据数量: {company_count}")
        
        # 测试贸易数据统计
        trade_count = processor.get_update_data_count(processor.table_source_trade, target_date)
        print(f"贸易表更新数据数量: {trade_count}")
        
    except Exception as e:
        print(f"数据库连接测试失败: {e}")
        print("这是正常的，如果数据库配置不正确的话")


def test_batch_iterator():
    """测试分批迭代器（模拟）"""
    print("\n" + "=" * 50)
    print("测试分批迭代器逻辑")
    print("=" * 50)
    
    try:
        processor = UpdateTimeProcessor()
        target_date = processor.get_target_date()
        
        print(f"目标日期: {target_date}")
        print("尝试获取第一批数据...")
        
        # 尝试获取公司数据的第一批
        iterator = processor.get_update_data_batch_iterator(
            processor.table_source_company, target_date, batch_size=10
        )
        
        batch_count = 0
        for data_raw, current_batch, total_batches in iterator:
            batch_count += 1
            print(f"获取到批次 {current_batch}/{total_batches}，数据量: {len(data_raw)}")
            if batch_count >= 2:  # 只测试前两批
                break
                
    except Exception as e:
        print(f"分批迭代器测试失败: {e}")
        print("这是正常的，如果数据库中没有对应数据的话")


def main():
    """主测试函数"""
    print("开始测试UpdateTimeProcessor...")
    
    # 测试时间逻辑（不需要数据库连接）
    test_time_logic()
    
    # 测试数据库相关功能（需要数据库连接）
    test_data_count()
    test_batch_iterator()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)


if __name__ == '__main__':
    main()
