class HashSharding:
    def __init__(self, num_shards):
        self.num_shards = num_shards
        self.shards = {}

    def get_shard(self, key):
        shard_id = hash(key) % self.num_shards
        if shard_id not in self.shards:
            self.shards[shard_id] = {}
        return self.shards[shard_id]

    def store_data(self, key, data):
        shard = self.get_shard(key)
        shard[key] = data
        print(f"[self.shards] {self.shards}")

    def retrieve_data(self, key):
        shard = self.get_shard(key)
        return shard.get(key, None)

if __name__ == '__main__':
    # 示例用法
    sharding = HashSharding(num_shards=4)

    # 存储数据
    sharding.store_data("key1", "data1")
    sharding.store_data("key2", "data2")

    # 查询数据
    data1 = sharding.retrieve_data("key1")
    data2 = sharding.retrieve_data("key2")

    print(data1)  # 输出: data1
    print(data2)  # 输出: data2
