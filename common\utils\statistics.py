import time
from functools import wraps
from setting import *


def statistics():
    """
    程序统计信息 - 装饰器版
    """
    def decorator(func):
        @wraps(func)
        def duration(*arg, **kwargs):
            start = time.time()
            total = func(*arg, **kwargs)
            end = time.time()
            info = {
                "status": "complete",
                "start_time": timestamp2str(start),
                "end_time": timestamp2str(end),
                "duration_time": f"{round(end - start, 2)} sec",
            }
            if isinstance(total, int):
                info["quantity_completion"] = total
                res_time = end - start
                if not res_time:
                    res_time = 0.001
                info["average_speed"] = f"{round(total / res_time, 2)} items/sec"
            logger.info(info)
            return total

        return duration

    return decorator


def timestamp2str(timestamp):
    return time.strftime('%F %H:%M:%S', time.localtime(timestamp))


@statistics()
def test():
    time.sleep(1)
    return 1000


if __name__ == '__main__':
    test()
