import hashlib


class ProcessStr:

    @staticmethod
    def is_zh(src: str):
        """判断一个字符是否是中文"""
        return True if '\u4e00' <= src <= '\u9fff' else False

    @staticmethod
    def is_en(src: str):
        """判断一个字符是否是英文文"""
        ascii_id = ord(src)
        return True if 65 <= ascii_id <= 90 or 97 <= ascii_id <= 122 else False


    @staticmethod
    def md5(src: str) -> str:
        return hashlib.md5(src.encode()).hexdigest()
