# 多进程版本 UpdateTimeProcessor 使用说明

## 概述

多进程版本的 `UpdateTimeProcessor` 是对原有单线程处理器的重大升级，通过进程池并行处理数据批次，显著提升处理性能。

## 主要改进 🚀

### 1. 多进程架构
- **进程池管理**：使用 `multiprocessing.Pool` 管理工作进程
- **任务分发**：将数据批次分发给不同进程并行处理
- **结果汇总**：收集各进程处理结果并统计

### 2. 智能进程配置
- **默认配置**：`min(CPU核心数, 8)` 个进程
- **环境变量**：通过 `MAX_WORKERS` 环境变量指定
- **参数指定**：创建实例时指定进程数

### 3. 独立工作函数
- **process_company_batch_worker**：处理公司数据批次
- **process_trade_batch_worker**：处理贸易数据批次
- **进程隔离**：每个进程独立创建处理器实例

## 性能提升 📈

### 理论性能对比
| 进程数 | 预期加速比 | 适用场景 |
|--------|------------|----------|
| 2进程  | 1.5-1.8x   | 小数据量 |
| 4进程  | 2.5-3.2x   | 中等数据量 |
| 8进程  | 4.0-5.5x   | 大数据量 |

### 实际效果
- **I/O密集型**：数据库读写操作并行化
- **CPU密集型**：数据清洗和转换并行化
- **内存优化**：批次处理避免内存溢出

## 使用方法

### 基本使用

```python
from wmb_update_process import UpdateTimeProcessor

# 使用默认进程数
processor = UpdateTimeProcessor()
processor.run()

# 指定进程数
processor = UpdateTimeProcessor(max_workers=4)
processor.run()
```

### 环境变量配置

```bash
# 指定进程数为6
export MAX_WORKERS=6
python wmb_update_process.py

# Windows
set MAX_WORKERS=6
python wmb_update_process.py

# 一次性设置
MAX_WORKERS=6 python wmb_update_process.py
```

### 命令行运行

```bash
# 默认配置运行
python wmb_update_process.py

# 后台运行
nohup python wmb_update_process.py > ./logs/wmb_multiprocess.log 2>&1 &

# 指定进程数后台运行
nohup env MAX_WORKERS=6 python wmb_update_process.py > ./logs/wmb_multiprocess.log 2>&1 &
```

## 架构设计

### 主进程职责
1. **数据查询**：获取需要处理的数据批次
2. **任务分发**：将批次分发给工作进程
3. **进度监控**：记录处理进度和统计信息
4. **结果汇总**：收集处理结果和错误信息

### 工作进程职责
1. **数据处理**：执行具体的数据清洗逻辑
2. **数据库操作**：独立的数据库连接和写入
3. **错误处理**：捕获和报告处理异常
4. **结果返回**：返回处理统计信息

### 数据流程
```
主进程 -> 查询批次数据 -> 进程池分发 -> 工作进程处理 -> 结果汇总 -> 完成
   |                        |              |
   v                        v              v
时间判断              并行数据处理      错误统计
数据统计              数据库写入      成功统计
```

## 配置建议

### 进程数选择
- **CPU密集型**：进程数 = CPU核心数
- **I/O密集型**：进程数 = CPU核心数 × 2
- **混合型**：进程数 = CPU核心数 × 1.5
- **内存限制**：根据可用内存调整

### 批次大小优化
- **小批次**：更好的并行度，但开销较大
- **大批次**：减少开销，但并行度降低
- **推荐**：保持默认的 50,000 条/批次

### 数据库连接
- 每个进程独立创建数据库连接
- 避免连接池竞争和死锁
- 自动重连和错误恢复

## 监控和日志

### 日志输出示例
```
2025-09-04 09:27:45 | INFO | 初始化多进程处理器，进程数: 8
2025-09-04 09:27:46 | INFO | 开始处理公司更新数据（多进程）
2025-09-04 09:27:47 | INFO | 准备使用 8 个进程处理 12 个批次
2025-09-04 09:27:48 | INFO | 进程 12345 开始处理公司数据批次 1/12
2025-09-04 09:27:49 | INFO | 进程 12346 开始处理公司数据批次 2/12
2025-09-04 09:27:50 | INFO | 批次 1 处理成功，处理 50000 条数据
```

### 错误处理
- **进程级错误**：单个进程失败不影响其他进程
- **批次重试**：可以针对失败批次进行重试
- **详细日志**：记录失败进程ID和错误信息

## 与单线程版本对比

| 特性 | 单线程版本 | 多进程版本 |
|------|------------|------------|
| 处理速度 | 基准 | 2-5倍提升 |
| 资源利用 | 单核CPU | 多核CPU |
| 内存使用 | 较低 | 中等（可控） |
| 错误恢复 | 全部重来 | 批次级重试 |
| 监控粒度 | 整体进度 | 批次级进度 |
| 复杂度 | 简单 | 中等 |

## 注意事项

### 系统要求
- **Python版本**：3.6+
- **内存要求**：建议8GB以上
- **CPU要求**：多核CPU效果更佳

### 使用限制
- **数据库连接数**：确保数据库支持足够的并发连接
- **网络带宽**：大量数据传输需要稳定网络
- **磁盘I/O**：日志写入可能成为瓶颈

### 最佳实践
1. **测试环境验证**：先在测试环境验证性能提升
2. **监控资源使用**：观察CPU、内存、网络使用情况
3. **逐步调优**：从较少进程数开始，逐步增加
4. **定期维护**：清理日志文件，监控数据库性能

## 故障排除

### 常见问题

1. **进程启动失败**
   - 检查系统资源是否充足
   - 确认数据库连接配置正确

2. **性能提升不明显**
   - 检查是否受I/O瓶颈限制
   - 调整批次大小和进程数

3. **内存使用过高**
   - 减少进程数或批次大小
   - 检查是否有内存泄漏

4. **数据库连接超限**
   - 减少进程数
   - 优化数据库连接池配置

### 性能调优
```bash
# 监控系统资源
htop
iostat -x 1
netstat -an | grep :3306

# 调整进程数
MAX_WORKERS=4 python wmb_update_process.py

# 监控处理进度
tail -f ./logs/wmb_multiprocess.log
```

## 测试验证

运行测试脚本验证功能：
```bash
python test_multiprocess_update.py
```

测试内容包括：
- 多进程配置测试
- 时间判断逻辑验证
- 数据统计功能测试
- 性能对比分析
- 错误处理机制验证
