import os
import sys
import yaml
import argparse
from pathlib import Path
from loguru import logger

# 项目目录
DIR_PROJECT = Path(__file__).resolve().parent

# 命令行参数解析
parser = argparse.ArgumentParser()
parser.add_argument('-c', '--conf', default=None, help='conf file path')

# 参数字典
ARGS = parser.parse_args()

# 配置文件
file_yml_env = os.environ.get("conf", None)
file_yml_arg = ARGS.conf
file_yml_default = ["./config.yaml", "./config.yaml"]
if file_yml_arg is not None:
    file_yml = file_yml_arg
elif file_yml_env is not None:
    file_yml = file_yml_env
else:
    for file in file_yml_default:
        if Path(file).exists():
            file_yml = file
            break
    else:
        raise FileNotFoundError("config.yaml not found")
# 读取配置文件
with open(file_yml, encoding='utf8') as f:
    config_data = yaml.safe_load(f)

# 日志
LEVEL_LIST = [  # 只会显示高于当前级别的日志信息
    'CRITICAL',  # 严重错误
    'ERROR',
    'WARNING',
    'INFO',
    'DEBUG'  # 调试
    'TRACE',  # 追踪
]
DIR_LOGS = Path(DIR_PROJECT, 'logs')
DIR_LOGS.mkdir(exist_ok=True)
FILE_ERROR = Path(DIR_LOGS, f'error.log')
LOG_LEVEL_DEFAULT = "INFO"
LOG_LEVEL = config_data.get('log', dict()).get('level', LOG_LEVEL_DEFAULT).upper()
logger.remove()
logger.add(sink=FILE_ERROR, level="WARNING", encoding='utf8', mode='w')
logger.add(sink=sys.stdout, level=LOG_LEVEL)


MYSQL_INFO_TARGET = dict(
    host='*********',
    # user='data_up',
    # passwd='q75%QBOJ3I6N',
    user='data_server',
    passwd='7VSZ~a5qQ@mjsE',
    charset='utf8mb4',
    # db='src_dy',
    db='db_customs',
)

MYSQL_INFO_SOURCE = dict(
    # host='*************',
    # user='root',
    # passwd='Upkuajing@2022',
    # charset='utf8mb4',
    # db='db_wmb_raw_rt',
    host='*********',
    # user='data_up',
    # passwd='q75%QBOJ3I6N',
    user='data_server',
    passwd='7VSZ~a5qQ@mjsE',
    charset='utf8mb4',
    db='src_wmb',
)

BATCH_SIZE = 50000
