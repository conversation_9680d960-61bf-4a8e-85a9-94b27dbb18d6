"""
File Name: wmb_manual_process.py
Model:
Description: 手动执行，处理指定公司ID列表的数据
Author:
Date: 2025-09-03
"""
from typing import List, Union
from wmb_data_process import WMB
from setting import logger
import sys


class ManualProcessor(WMB):
    """手动数据处理器 - 基于公司ID列表"""

    def __init__(self):
        super().__init__()

    def get_company_data_by_ids(self, company_ids: List[Union[int, str]]) -> List:
        """
        根据公司ID列表获取公司数据

        Args:
            company_ids: 公司ID列表

        Returns:
            List: 公司数据列表
        """
        if not company_ids:
            logger.warning("公司ID列表为空")
            return []

        # 将ID列表转换为字符串格式，用于SQL查询
        id_str = ','.join([str(id) for id in company_ids])

        # 构建SQL查询语句
        fields_company_read = [
            'company_id', 'name', 'business', 'country', 'address', 'manager',
            'telephone', 'fax', 'email', 'website'
        ]

        sql = f"""
        SELECT {', '.join(fields_company_read)}
        FROM {self.table_source_company}
        WHERE company_id IN ({id_str})
        """

        logger.info(f"查询公司数据，ID列表: {company_ids}")
        data_raw = self.db_source.read(sql)

        if data_raw:
            logger.info(f"获取到 {len(data_raw)} 条公司数据")
        else:
            logger.warning("未获取到任何公司数据")

        return data_raw or []

    def get_trade_data_by_company_ids(self, company_ids: List[Union[int, str]]) -> List:
        """
        根据公司ID列表获取相关的贸易数据

        Args:
            company_ids: 公司ID列表

        Returns:
            List: 贸易数据列表
        """
        if not company_ids:
            logger.warning("公司ID列表为空")
            return []

        # 将ID列表转换为字符串格式，用于SQL查询
        id_str = ','.join([str(id) for id in company_ids])

        # 构建SQL查询语句 - 查询买方或卖方ID在列表中的贸易数据
        fields_trade_read = [
            'amount', 'bill_no', 'buyer', 'buyer_country', 'buyer_id_std', 'buyer_port',
            'container', 'date', 'descript', 'descript_label', 'hs', 'id', 'notify_name',
            'origin_country', 'qty', 'qty_unit', 'seller', 'seller_country', 'seller_id_std',
            'seller_port', 'trans', 'weight', 'weight_unit', 'uusd'
        ]

        sql = f"""
        SELECT {', '.join(fields_trade_read)}
        FROM {self.table_source_trade}
        WHERE company_id IN ({id_str})
        """

        logger.info(f"查询贸易数据，公司ID列表: {company_ids}")
        data_raw = self.db_source.read(sql)

        if data_raw:
            logger.info(f"获取到 {len(data_raw)} 条贸易数据")
        else:
            logger.warning("未获取到任何贸易数据")

        return data_raw or []

    def process_companies_by_ids(self, company_ids: List[Union[int, str]]):
        """
        处理指定ID列表的公司数据

        Args:
            company_ids: 公司ID列表
        """
        logger.info("=" * 50)
        logger.info("开始处理指定公司数据")
        logger.info(f"公司ID列表: {company_ids}")
        logger.info("=" * 50)

        # 获取公司数据
        company_data = self.get_company_data_by_ids(company_ids)

        if not company_data:
            logger.warning("没有找到对应的公司数据，跳过处理")
            return

        # 使用现有的处理逻辑
        logger.info("开始清洗公司数据...")
        self.processing_company(company_data)
        logger.info("公司数据处理完成")

    def process_trades_by_company_ids(self, company_ids: List[Union[int, str]]):
        """
        处理指定公司ID相关的贸易数据

        Args:
            company_ids: 公司ID列表
        """
        logger.info("=" * 50)
        logger.info("开始处理相关贸易数据")
        logger.info(f"公司ID列表: {company_ids}")
        logger.info("=" * 50)

        # 初始化必要的映射数据
        logger.info("正在加载港口映射和单位映射...")
        self.mapping_port = self.read_customs_port_map()
        self.mapping_quantity, self.mapping_weight = self.read_mapping_unit()
        logger.info("映射数据加载完成")

        # 获取贸易数据
        trade_data = self.get_trade_data_by_company_ids(company_ids)

        if not trade_data:
            logger.warning("没有找到对应的贸易数据，跳过处理")
            return

        # 使用现有的处理逻辑
        logger.info("开始清洗贸易数据...")
        self.processing_trade(trade_data)
        logger.info("贸易数据处理完成")

    def run(self, company_ids: List[Union[int, str]], process_companies: bool = True, process_trades: bool = True):
        """
        运行手动处理流程

        Args:
            company_ids: 公司ID列表
            process_companies: 是否处理公司数据，默认True
            process_trades: 是否处理贸易数据，默认True
        """
        if not company_ids:
            logger.error("公司ID列表不能为空")
            return

        logger.info("=" * 60)
        logger.info("开始执行手动数据处理任务")
        logger.info(f"目标公司ID: {company_ids}")
        logger.info(f"处理公司数据: {process_companies}")
        logger.info(f"处理贸易数据: {process_trades}")
        logger.info("=" * 60)

        try:
            # 处理公司数据
            if process_companies:
                self.process_companies_by_ids(company_ids)

            # 处理贸易数据
            if process_trades:
                self.process_trades_by_company_ids(company_ids)

            logger.info("=" * 60)
            logger.info("手动数据处理任务完成！")
            logger.info("=" * 60)

        except Exception as e:
            logger.error("=" * 60)
            logger.error("手动数据处理任务异常终止！")
            logger.error(f"错误信息: {str(e)}")
            logger.error("=" * 60)
            raise


def main():
    """主函数 - 使用示例"""

    # 示例: 要处理的公司ID列表（请根据实际需要修改）
    target_company_ids = [6192910]
    try:
        processor = ManualProcessor()

        # 运行处理流程
        processor.run(
            company_ids=target_company_ids,
            process_companies=True,  # 是否处理公司数据
            process_trades=True      # 是否处理贸易数据
        )
        from monitor import send_text_message
        send_text_message(f"外贸邦数据处理完成")
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        msg = f"{exc_type.__name__}: {e}"
        logger.warning(msg)
        send_text_message(f"外贸邦数据处理异常中止：{msg}")



if __name__ == '__main__':
    main()