"""
测试ManualProcessor的功能
"""
from wmb_manual_process import ManualProcessor, read_company_ids_from_file


def test_data_retrieval():
    """测试数据获取功能"""
    print("=" * 50)
    print("测试数据获取功能")
    print("=" * 50)
    
    try:
        processor = ManualProcessor()
        
        # 测试公司ID列表
        test_company_ids = [12345, 67890]
        
        print(f"测试公司ID列表: {test_company_ids}")
        
        # 测试获取公司数据
        print("\n1. 测试获取公司数据...")
        company_data = processor.get_company_data_by_ids(test_company_ids)
        print(f"获取到公司数据数量: {len(company_data)}")
        
        # 测试获取贸易数据
        print("\n2. 测试获取贸易数据...")
        trade_data = processor.get_trade_data_by_company_ids(test_company_ids)
        print(f"获取到贸易数据数量: {len(trade_data)}")
        
    except Exception as e:
        print(f"数据获取测试失败: {e}")
        print("这是正常的，如果数据库中没有对应数据的话")


def test_file_reading():
    """测试从文件读取公司ID功能"""
    print("\n" + "=" * 50)
    print("测试文件读取功能")
    print("=" * 50)
    
    # 创建测试文件
    test_file = "test_company_ids.txt"
    test_ids = [12345, 67890, 11111, 22222]
    
    try:
        # 写入测试文件
        with open(test_file, 'w', encoding='utf-8') as f:
            for company_id in test_ids:
                f.write(f"{company_id}\n")
        
        print(f"创建测试文件: {test_file}")
        print(f"写入ID: {test_ids}")
        
        # 测试读取
        read_ids = read_company_ids_from_file(test_file)
        print(f"读取到的ID: {read_ids}")
        
        # 验证结果
        if read_ids == test_ids:
            print("✅ 文件读取测试通过")
        else:
            print("❌ 文件读取测试失败")
        
        # 清理测试文件
        import os
        os.remove(test_file)
        print(f"清理测试文件: {test_file}")
        
    except Exception as e:
        print(f"文件读取测试失败: {e}")


def test_empty_list():
    """测试空列表处理"""
    print("\n" + "=" * 50)
    print("测试空列表处理")
    print("=" * 50)
    
    try:
        processor = ManualProcessor()
        
        # 测试空列表
        empty_list = []
        
        print("测试空公司ID列表...")
        company_data = processor.get_company_data_by_ids(empty_list)
        trade_data = processor.get_trade_data_by_company_ids(empty_list)
        
        if len(company_data) == 0 and len(trade_data) == 0:
            print("✅ 空列表处理测试通过")
        else:
            print("❌ 空列表处理测试失败")
            
    except Exception as e:
        print(f"空列表测试失败: {e}")


def test_sql_generation():
    """测试SQL语句生成"""
    print("\n" + "=" * 50)
    print("测试SQL语句生成逻辑")
    print("=" * 50)
    
    # 测试ID列表转换
    test_cases = [
        [12345],
        [12345, 67890],
        [12345, 67890, 11111, 22222],
        ["12345", "67890"],  # 字符串ID
        [12345, "67890", 11111]  # 混合类型
    ]
    
    for i, company_ids in enumerate(test_cases, 1):
        id_str = ','.join([str(id) for id in company_ids])
        print(f"测试用例 {i}: {company_ids} -> {id_str}")


def main():
    """主测试函数"""
    print("开始测试ManualProcessor...")
    
    # 测试SQL生成逻辑（不需要数据库连接）
    test_sql_generation()
    
    # 测试文件读取功能
    test_file_reading()
    
    # 测试空列表处理
    test_empty_list()
    
    # 测试数据库相关功能（需要数据库连接）
    test_data_retrieval()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)


if __name__ == '__main__':
    main()
