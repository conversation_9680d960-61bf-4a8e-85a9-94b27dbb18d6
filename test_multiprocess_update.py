"""
测试多进程版本的UpdateTimeProcessor
"""
import os
import time
from datetime import datetime
from wmb_update_process import UpdateTimeProcessor


def test_multiprocess_config():
    """测试多进程配置"""
    print("=" * 50)
    print("测试多进程配置")
    print("=" * 50)
    
    # 测试默认配置
    processor1 = UpdateTimeProcessor()
    print(f"默认进程数: {processor1.max_workers}")
    
    # 测试指定进程数
    processor2 = UpdateTimeProcessor(max_workers=4)
    print(f"指定进程数: {processor2.max_workers}")
    
    # 测试环境变量配置
    os.environ['MAX_WORKERS'] = '6'
    max_workers = int(os.environ.get('MAX_WORKERS', 0)) or None
    processor3 = UpdateTimeProcessor(max_workers=max_workers)
    print(f"环境变量进程数: {processor3.max_workers}")
    
    # 清理环境变量
    if 'MAX_WORKERS' in os.environ:
        del os.environ['MAX_WORKERS']


def test_time_logic():
    """测试时间判断逻辑（多进程版本）"""
    print("\n" + "=" * 50)
    print("测试时间判断逻辑（多进程版本）")
    print("=" * 50)
    
    processor = UpdateTimeProcessor(max_workers=2)
    
    # 测试不同时间点
    test_times = [
        datetime(2025, 9, 3, 8, 30),   # 8:30 - 应该取昨天
        datetime(2025, 9, 3, 15, 30),  # 15:30 - 应该取当天
    ]
    
    for test_time in test_times:
        processor.current_time = test_time
        target_date = processor.get_target_date()
        expected = "昨天" if test_time.hour < 12 else "当天"
        print(f"测试时间: {test_time.strftime('%H:%M:%S')} -> 目标日期: {target_date} ({expected})")


def test_data_count():
    """测试数据统计功能（多进程版本）"""
    print("\n" + "=" * 50)
    print("测试数据统计功能（多进程版本）")
    print("=" * 50)
    
    try:
        processor = UpdateTimeProcessor(max_workers=2)
        target_date = processor.get_target_date()
        
        # 测试公司数据统计
        company_count = processor.get_update_data_count(processor.table_source_company, target_date)
        print(f"公司表更新数据数量: {company_count}")
        
        # 测试贸易数据统计
        trade_count = processor.get_update_data_count(processor.table_source_trade, target_date)
        print(f"贸易表更新数据数量: {trade_count}")
        
    except Exception as e:
        print(f"数据库连接测试失败: {e}")
        print("这是正常的，如果数据库配置不正确的话")


def test_batch_processing_simulation():
    """模拟批次处理测试"""
    print("\n" + "=" * 50)
    print("模拟批次处理测试")
    print("=" * 50)
    
    # 模拟批次数据
    mock_batches = [
        ([f"data_{i}" for i in range(10)], 1, 3, "2025-09-02"),
        ([f"data_{i}" for i in range(15)], 2, 3, "2025-09-02"),
        ([f"data_{i}" for i in range(8)], 3, 3, "2025-09-02"),
    ]
    
    print("模拟公司数据批次处理:")
    for batch_data in mock_batches:
        data_raw, current_batch, total_batches, target_date = batch_data
        print(f"批次 {current_batch}/{total_batches}: {len(data_raw)} 条数据")
    
    print("\n模拟贸易数据批次处理:")
    # 模拟贸易数据批次（包含映射数据）
    mock_trade_batches = [
        ([f"trade_{i}" for i in range(20)], 1, 2, "2025-09-02", {}, {}, {}),
        ([f"trade_{i}" for i in range(25)], 2, 2, "2025-09-02", {}, {}, {}),
    ]
    
    for batch_data in mock_trade_batches:
        data_raw, current_batch, total_batches, target_date, _, _, _ = batch_data
        print(f"批次 {current_batch}/{total_batches}: {len(data_raw)} 条数据")


def test_performance_comparison():
    """性能对比测试（模拟）"""
    print("\n" + "=" * 50)
    print("性能对比测试（模拟）")
    print("=" * 50)
    
    # 模拟单进程处理时间
    single_process_time = 10.5  # 假设单进程需要10.5秒
    
    # 模拟多进程处理时间（理论上应该更快）
    multi_process_configs = [
        (2, 6.2),   # 2进程，6.2秒
        (4, 3.8),   # 4进程，3.8秒
        (8, 2.5),   # 8进程，2.5秒
    ]
    
    print(f"模拟单进程处理时间: {single_process_time}秒")
    print("\n多进程性能对比:")
    
    for workers, time_taken in multi_process_configs:
        speedup = single_process_time / time_taken
        efficiency = speedup / workers * 100
        print(f"  {workers}进程: {time_taken}秒, 加速比: {speedup:.2f}x, 效率: {efficiency:.1f}%")


def test_error_handling():
    """测试错误处理机制"""
    print("\n" + "=" * 50)
    print("测试错误处理机制")
    print("=" * 50)
    
    try:
        # 测试无效的进程数
        processor = UpdateTimeProcessor(max_workers=0)
        print(f"进程数为0时的实际配置: {processor.max_workers}")
        
        # 测试负数进程数
        processor = UpdateTimeProcessor(max_workers=-1)
        print(f"进程数为负数时的实际配置: {processor.max_workers}")
        
    except Exception as e:
        print(f"错误处理测试: {e}")


def main():
    """主测试函数"""
    print("开始测试多进程版本的UpdateTimeProcessor...")
    
    # 测试多进程配置
    test_multiprocess_config()
    
    # 测试时间逻辑
    test_time_logic()
    
    # 测试数据统计
    test_data_count()
    
    # 模拟批次处理
    test_batch_processing_simulation()
    
    # 性能对比
    test_performance_comparison()
    
    # 错误处理
    test_error_handling()
    
    print("\n" + "=" * 50)
    print("多进程测试完成！")
    print("=" * 50)
    print("\n使用说明:")
    print("1. 默认使用 min(CPU核心数, 8) 个进程")
    print("2. 可通过环境变量 MAX_WORKERS 指定进程数")
    print("3. 可在创建实例时指定: UpdateTimeProcessor(max_workers=4)")
    print("4. 运行: python wmb_update_process.py")
    print("5. 指定进程数运行: MAX_WORKERS=6 python wmb_update_process.py")


if __name__ == '__main__':
    main()
