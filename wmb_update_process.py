"""
File Name: wmb_update_process.py
Model:
Description: 以当天更新时间且创建时间不等于更新时间数据，进行处理
Author:
Date: 2025-09-03
"""
from wmb_data_process import WMB
from typing import Union



class UpdateProcess(WMB):
    def __init__(self):
        super().__init__()

    def read_update_data(self, update_date: str) -> Union[tuple, None]:
        """
        查询更新时间不等于创建时间的数据
        """
        sql = f"select id from {self.table_source_company} where create_time between {update_date} and {update_date} and create_time != update_time"
        data_raw = self.db_source.read(sql)
        return data_raw