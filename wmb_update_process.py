"""
File Name: wmb_update_process.py
Model:
Description: 基于更新时间取数据的处理器，支持时间判断和分批处理
Author:
Date: 2025-09-03
"""
import os
import multiprocessing as mp
from datetime import datetime, timedelta
from typing import Iterator, Tuple, List, Dict, Any
from wmb_data_process import WMB
from setting import BATCH_SIZE, logger

# ==================== 全局映射变量 ====================
# 这些映射数据在程序运行期间不会修改，可以设为全局变量避免重复传参
GLOBAL_MAPPING_PORT = None
GLOBAL_MAPPING_QUANTITY = None
GLOBAL_MAPPING_WEIGHT = None


def init_global_mappings():
    """初始化全局映射变量"""
    global GLOBAL_MAPPING_PORT, GLOBAL_MAPPING_QUANTITY, GLOBAL_MAPPING_WEIGHT

    if GLOBAL_MAPPING_PORT is None:
        logger.info("正在初始化全局映射数据...")
        temp_processor = UpdateTimeProcessor(max_workers=1)

        # 加载映射数据
        GLOBAL_MAPPING_PORT = temp_processor.read_customs_port_map()
        GLOBAL_MAPPING_QUANTITY, GLOBAL_MAPPING_WEIGHT = temp_processor.read_mapping_unit()

        logger.info(f"全局映射数据初始化完成 - 港口映射: {len(GLOBAL_MAPPING_PORT)} 条, "
                   f"数量单位: {len(GLOBAL_MAPPING_QUANTITY)} 条, 重量单位: {len(GLOBAL_MAPPING_WEIGHT)} 条")
    else:
        logger.info("全局映射数据已存在，跳过初始化")


class UpdateTimeProcessor(WMB):
    """基于更新时间的数据处理器"""

    def __init__(self, max_workers: int = None):
        super().__init__()
        self.current_time = datetime.now()

        # 多进程配置
        self.max_workers = max_workers or min(mp.cpu_count(), 8)  # 默认最多8个进程
        logger.info(f"初始化多进程处理器，进程数: {self.max_workers}")

    def get_target_date(self) -> str:
        """
        根据当前执行时间判断取数据的日期
        12点前取昨天，12点后取当天
        """
        if self.current_time.hour < 12:
            # 12点前取昨天的数据
            target_date = (self.current_time - timedelta(days=1)).strftime('%Y-%m-%d')
            logger.info(f"执行时间: {self.current_time.strftime('%H:%M:%S')}, 取昨天数据: {target_date}")
        else:
            # 12点后取当天的数据
            target_date = self.current_time.strftime('%Y-%m-%d')
            logger.info(f"执行时间: {self.current_time.strftime('%H:%M:%S')}, 取当天数据: {target_date}")

        return target_date

    def get_update_data_count(self, table_name: str, target_date: str) -> int:
        """
        获取指定日期更新且创建时间≠更新时间的数据总数
        """
        sql = f"""
        SELECT COUNT(*)
        FROM {table_name}
        WHERE DATE(update_time) = '{target_date}'
        AND create_time != update_time
        """
        result = self.db_source.read(sql)
        count = result[0][0] if result and result[0] else 0
        logger.info(f"表 {table_name} 在 {target_date} 的更新数据总数: {count}")
        return count

    def get_update_data_batch_iterator(self, table_name: str, target_date: str,
                                     batch_size: int = BATCH_SIZE) -> Iterator[Tuple[List, int, int]]:
        """
        基于时间范围的分批数据迭代器
        由于ID乱序，使用LIMIT OFFSET方式分批获取数据

        Returns:
            Iterator[Tuple[List, int, int]]: (数据列表, 当前批次, 总批次数)
        """
        # 获取总数据量
        total_count = self.get_update_data_count(table_name, target_date)
        if total_count == 0:
            logger.warning(f"表 {table_name} 在 {target_date} 没有找到需要处理的数据")
            return

        # 计算总批次数
        total_batches = (total_count + batch_size - 1) // batch_size
        logger.info(f"开始分批处理，总数据量: {total_count}, 批次大小: {batch_size}, 总批次: {total_batches}")

        # 分批获取数据
        for batch_num in range(total_batches):
            offset = batch_num * batch_size

            if table_name == self.table_source_company:
                # 公司数据查询
                sql = f"""
                SELECT company_id, name, business, country, address, manager,
                       telephone, fax, email, website
                FROM {table_name}
                WHERE DATE(update_time) = '{target_date}'
                AND create_time != update_time
                ORDER BY update_time
                LIMIT {batch_size} OFFSET {offset}
                """
            elif table_name == self.table_source_trade:
                # 贸易数据查询
                sql = f"""
                SELECT amount, bill_no, buyer, buyer_country, buyer_id_std, buyer_port,
                       container, date, descript, descript_label, hs, id, notify_name,
                       origin_country, qty, qty_unit, seller, seller_country, seller_id_std,
                       seller_port, trans, weight, weight_unit, uusd
                FROM {table_name}
                WHERE DATE(update_time) = '{target_date}'
                AND create_time != update_time
                ORDER BY update_time
                LIMIT {batch_size} OFFSET {offset}
                """
            else:
                raise ValueError(f"不支持的表名: {table_name}")

            logger.info(f"正在获取第 {batch_num + 1}/{total_batches} 批数据，偏移量: {offset}")
            data_raw = self.db_source.read(sql)

            if data_raw:
                logger.info(f"第 {batch_num + 1} 批获取到 {len(data_raw)} 条数据")
                yield data_raw, batch_num + 1, total_batches
            else:
                logger.warning(f"第 {batch_num + 1} 批没有获取到数据")

    def collect_company_tasks(self, target_date: str) -> List[Tuple]:
        """收集公司数据处理任务"""
        logger.info("正在收集公司数据任务...")

        batch_tasks = []
        for data_raw, current_batch, total_batches in self.get_update_data_batch_iterator(
            self.table_source_company, target_date
        ):
            batch_tasks.append(('company', data_raw, current_batch, total_batches, target_date))

        logger.info(f"收集到 {len(batch_tasks)} 个公司数据批次任务")
        return batch_tasks

    def collect_trade_tasks(self, target_date: str) -> List[Tuple]:
        """收集贸易数据处理任务"""
        logger.info("正在收集贸易数据任务...")

        batch_tasks = []
        for data_raw, current_batch, total_batches in self.get_update_data_batch_iterator(
            self.table_source_trade, target_date
        ):
            batch_tasks.append(('trade', data_raw, current_batch, total_batches, target_date))

        logger.info(f"收集到 {len(batch_tasks)} 个贸易数据批次任务")
        return batch_tasks

    def run(self):
        """运行完整的更新数据处理流程（统一任务分发版本）"""
        start_time = datetime.now()
        logger.info("=" * 60)
        logger.info("开始执行基于更新时间的数据处理任务（统一任务分发）")
        logger.info(f"任务开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"使用进程数: {self.max_workers}")
        logger.info("=" * 60)

        try:
            # 初始化全局映射数据
            init_global_mappings()

            # 获取目标日期
            target_date = self.get_target_date()

            # 收集所有任务
            logger.info("正在收集所有数据处理任务...")
            all_tasks = []

            # 收集公司数据任务
            company_tasks = self.collect_company_tasks(target_date)
            all_tasks.extend(company_tasks)

            # 收集贸易数据任务
            trade_tasks = self.collect_trade_tasks(target_date)
            all_tasks.extend(trade_tasks)

            if not all_tasks:
                logger.warning("没有找到需要处理的数据任务")
                return

            logger.info(f"总共收集到 {len(all_tasks)} 个任务 "
                       f"(公司: {len(company_tasks)}, 贸易: {len(trade_tasks)})")

            # 统一使用进程池处理所有任务
            logger.info("=" * 50)
            logger.info("开始统一任务分发处理")
            logger.info("=" * 50)

            processed_count = 0
            failed_batches = []

            with mp.Pool(processes=self.max_workers) as pool:
                # 提交所有任务
                results = pool.map(unified_batch_worker, all_tasks)

                # 汇总结果
                company_processed = 0
                trade_processed = 0

                for result in results:
                    if result['success']:
                        processed_count += result['processed_count']
                        if result['task_type'] == 'company':
                            company_processed += result['processed_count']
                        else:
                            trade_processed += result['processed_count']
                        logger.info(f"{result['task_type']}批次 {result['batch_num']} 处理成功，"
                                   f"处理 {result['processed_count']} 条数据")
                    else:
                        failed_batches.append((result['task_type'], result['batch_num']))
                        logger.error(f"{result['task_type']}批次 {result['batch_num']} 处理失败: {result['error']}")

            # 处理结果统计
            end_time = datetime.now()
            duration = end_time - start_time

            logger.info("=" * 60)
            if failed_batches:
                logger.error(f"数据处理完成，但有 {len(failed_batches)} 个批次失败: {failed_batches}")
            else:
                logger.info("数据处理任务完成！")

            logger.info(f"处理统计 - 公司数据: {company_processed} 条, 贸易数据: {trade_processed} 条")
            logger.info(f"总计处理: {processed_count} 条数据")
            logger.info(f"任务结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"总耗时: {duration}")
            logger.info("=" * 60)

        except Exception as e:
            end_time = datetime.now()
            duration = end_time - start_time

            logger.error("=" * 60)
            logger.error("数据处理任务异常终止！")
            logger.error(f"错误信息: {str(e)}")
            logger.error(f"任务结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.error(f"运行时长: {duration}")
            logger.error("=" * 60)
            raise


def main():
    """主函数"""
    try:
        # 可以通过环境变量指定进程数
        max_workers = int(os.environ.get('MAX_WORKERS', 0)) or None
        processor = UpdateTimeProcessor(max_workers=max_workers)
        processor.run()

        # 发送成功通知（如果有监控系统）
        try:
            from monitor import send_text_message
            send_text_message("外贸邦更新数据处理完成（多进程版本）")
        except ImportError:
            logger.info("监控模块未找到，跳过消息发送")

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")

        # 发送失败通知（如果有监控系统）
        try:
            from monitor import send_text_message
            send_text_message(f"外贸邦更新数据处理异常中止：{str(e)}")
        except ImportError:
            logger.error("监控模块未找到，无法发送错误通知")

        raise


# ==================== 多进程工作函数 ====================

def unified_batch_worker(task_data: Tuple[str, List, int, int, str]) -> Dict[str, Any]:
    """
    统一的多进程工作函数：处理单个批次的数据

    Args:
        task_data: (任务类型, 数据列表, 当前批次, 总批次数, 目标日期)

    Returns:
        Dict: 处理结果统计
    """
    task_type, data_raw, current_batch, total_batches, target_date = task_data

    try:
        # 创建新的处理器实例（每个进程独立）
        processor = UpdateTimeProcessor(max_workers=1)

        logger.info(f"进程 {os.getpid()} 开始处理{task_type}数据批次 {current_batch}/{total_batches}")

        # 根据任务类型处理数据
        if task_type == 'company':
            processor.processing_company(data_raw)
        elif task_type == 'trade':
            # 使用全局映射数据
            processor.mapping_port = GLOBAL_MAPPING_PORT
            processor.mapping_quantity = GLOBAL_MAPPING_QUANTITY
            processor.mapping_weight = GLOBAL_MAPPING_WEIGHT
            processor.processing_trade(data_raw)
        else:
            raise ValueError(f"不支持的任务类型: {task_type}")

        result = {
            'success': True,
            'task_type': task_type,
            'batch_num': current_batch,
            'processed_count': len(data_raw),
            'error': None,
            'process_id': os.getpid()
        }

        logger.info(f"进程 {os.getpid()} 完成{task_type}数据批次 {current_batch}，处理 {len(data_raw)} 条")
        return result

    except Exception as e:
        error_msg = f"进程 {os.getpid()} 处理{task_type}数据批次 {current_batch} 失败: {str(e)}"
        logger.error(error_msg)

        return {
            'success': False,
            'task_type': task_type,
            'batch_num': current_batch,
            'processed_count': 0,
            'error': error_msg,
            'process_id': os.getpid()
        }


def process_company_batch_worker(batch_data: Tuple[List, int, int, str]) -> Dict[str, Any]:
    """
    多进程工作函数：处理单个批次的公司数据（向后兼容）
    """
    data_raw, current_batch, total_batches, target_date = batch_data
    # 转换为统一格式并调用统一工作函数
    task_data = ('company', data_raw, current_batch, total_batches, target_date)
    return unified_batch_worker(task_data)


def process_trade_batch_worker(batch_data: Tuple[List, int, int, str, Dict, Dict, Dict]) -> Dict[str, Any]:
    """
    多进程工作函数：处理单个批次的贸易数据（向后兼容）
    """
    data_raw, current_batch, total_batches, target_date, mapping_port, mapping_quantity, mapping_weight = batch_data
    # 转换为统一格式并调用统一工作函数
    task_data = ('trade', data_raw, current_batch, total_batches, target_date)
    return unified_batch_worker(task_data)


if __name__ == '__main__':
    main()