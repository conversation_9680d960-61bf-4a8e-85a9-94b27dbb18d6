"""
File Name: wmb_update_process.py
Model:
Description: 基于更新时间取数据的处理器，支持时间判断和分批处理
Author:
Date: 2025-09-03
"""
from datetime import datetime, timedelta
from typing import Iterator, <PERSON><PERSON>, List
from wmb_data_process import WMB
from setting import BATCH_SIZE, logger


class UpdateTimeProcessor(WMB):
    """基于更新时间的数据处理器"""

    def __init__(self):
        super().__init__()
        self.current_time = datetime.now()

    def get_target_date(self) -> str:
        """
        根据当前执行时间判断取数据的日期
        12点前取昨天，12点后取当天
        """
        if self.current_time.hour < 12:
            # 12点前取昨天的数据
            target_date = (self.current_time - timedelta(days=1)).strftime('%Y-%m-%d')
            logger.info(f"执行时间: {self.current_time.strftime('%H:%M:%S')}, 取昨天数据: {target_date}")
        else:
            # 12点后取当天的数据
            target_date = self.current_time.strftime('%Y-%m-%d')
            logger.info(f"执行时间: {self.current_time.strftime('%H:%M:%S')}, 取当天数据: {target_date}")

        return target_date

    def get_update_data_count(self, table_name: str, target_date: str) -> int:
        """
        获取指定日期更新且创建时间≠更新时间的数据总数
        """
        sql = f"""
        SELECT COUNT(*)
        FROM {table_name}
        WHERE DATE(update_time) = '{target_date}'
        AND create_time != update_time
        """
        result = self.db_source.read(sql)
        count = result[0][0] if result and result[0] else 0
        logger.info(f"表 {table_name} 在 {target_date} 的更新数据总数: {count}")
        return count

    def get_update_data_batch_iterator(self, table_name: str, target_date: str,
                                     batch_size: int = BATCH_SIZE) -> Iterator[Tuple[List, int, int]]:
        """
        基于时间范围的分批数据迭代器
        由于ID乱序，使用LIMIT OFFSET方式分批获取数据

        Returns:
            Iterator[Tuple[List, int, int]]: (数据列表, 当前批次, 总批次数)
        """
        # 获取总数据量
        total_count = self.get_update_data_count(table_name, target_date)
        if total_count == 0:
            logger.warning(f"表 {table_name} 在 {target_date} 没有找到需要处理的数据")
            return

        # 计算总批次数
        total_batches = (total_count + batch_size - 1) // batch_size
        logger.info(f"开始分批处理，总数据量: {total_count}, 批次大小: {batch_size}, 总批次: {total_batches}")

        # 分批获取数据
        for batch_num in range(total_batches):
            offset = batch_num * batch_size

            if table_name == self.table_source_company:
                # 公司数据查询
                sql = f"""
                SELECT company_id, name, business, country, address, manager,
                       telephone, fax, email, website
                FROM {table_name}
                WHERE DATE(update_time) = '{target_date}'
                AND create_time != update_time
                ORDER BY update_time
                LIMIT {batch_size} OFFSET {offset}
                """
            elif table_name == self.table_source_trade:
                # 贸易数据查询
                sql = f"""
                SELECT amount, bill_no, buyer, buyer_country, buyer_id_std, buyer_port,
                       container, date, descript, descript_label, hs, id, notify_name,
                       origin_country, qty, qty_unit, seller, seller_country, seller_id_std,
                       seller_port, trans, weight, weight_unit, uusd
                FROM {table_name}
                WHERE DATE(update_time) = '{target_date}'
                AND create_time != update_time
                ORDER BY update_time
                LIMIT {batch_size} OFFSET {offset}
                """
            else:
                raise ValueError(f"不支持的表名: {table_name}")

            logger.info(f"正在获取第 {batch_num + 1}/{total_batches} 批数据，偏移量: {offset}")
            data_raw = self.db_source.read(sql)

            if data_raw:
                logger.info(f"第 {batch_num + 1} 批获取到 {len(data_raw)} 条数据")
                yield data_raw, batch_num + 1, total_batches
            else:
                logger.warning(f"第 {batch_num + 1} 批没有获取到数据")

    def run_company_update_process(self):
        """运行公司数据的更新处理流程"""
        target_date = self.get_target_date()

        logger.info("=" * 50)
        logger.info("开始处理公司更新数据")
        logger.info("=" * 50)

        processed_count = 0

        try:
            for data_raw, current_batch, total_batches in self.get_update_data_batch_iterator(
                self.table_source_company, target_date
            ):
                logger.info(f"处理公司数据 - 批次 {current_batch}/{total_batches}")

                # 复用现有的数据处理逻辑
                self.processing_company(data_raw)
                processed_count += len(data_raw)

                logger.info(f"批次 {current_batch} 处理完成，本批处理 {len(data_raw)} 条，累计处理 {processed_count} 条")

            logger.info(f"公司数据处理完成！总共处理 {processed_count} 条数据")

        except Exception as e:
            logger.error(f"公司数据处理出错: {str(e)}")
            raise

    def run_trade_update_process(self):
        """运行贸易数据的更新处理流程"""
        target_date = self.get_target_date()

        logger.info("=" * 50)
        logger.info("开始处理贸易更新数据")
        logger.info("=" * 50)

        # 初始化必要的映射数据（复用父类逻辑）
        logger.info("正在加载港口映射和单位映射...")
        self.mapping_port = self.read_customs_port_map()
        self.mapping_quantity, self.mapping_weight = self.read_mapping_unit()
        logger.info("映射数据加载完成")

        processed_count = 0

        try:
            for data_raw, current_batch, total_batches in self.get_update_data_batch_iterator(
                self.table_source_trade, target_date
            ):
                logger.info(f"处理贸易数据 - 批次 {current_batch}/{total_batches}")

                # 复用现有的数据处理逻辑
                self.processing_trade(data_raw)
                processed_count += len(data_raw)

                logger.info(f"批次 {current_batch} 处理完成，本批处理 {len(data_raw)} 条，累计处理 {processed_count} 条")

            logger.info(f"贸易数据处理完成！总共处理 {processed_count} 条数据")

        except Exception as e:
            logger.error(f"贸易数据处理出错: {str(e)}")
            raise

    def run(self):
        """运行完整的更新数据处理流程"""
        start_time = datetime.now()
        logger.info("=" * 60)
        logger.info("开始执行基于更新时间的数据处理任务")
        logger.info(f"任务开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

        try:
            # 处理公司数据
            self.run_company_update_process()

            # 处理贸易数据
            self.run_trade_update_process()

            end_time = datetime.now()
            duration = end_time - start_time

            logger.info("=" * 60)
            logger.info("数据处理任务完成！")
            logger.info(f"任务结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"总耗时: {duration}")
            logger.info("=" * 60)

        except Exception as e:
            end_time = datetime.now()
            duration = end_time - start_time

            logger.error("=" * 60)
            logger.error("数据处理任务异常终止！")
            logger.error(f"错误信息: {str(e)}")
            logger.error(f"任务结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.error(f"运行时长: {duration}")
            logger.error("=" * 60)
            raise


def main():
    """主函数"""
    try:
        processor = UpdateTimeProcessor()
        processor.run()

        # 发送成功通知（如果有监控系统）
        try:
            from monitor import send_text_message
            send_text_message("外贸邦更新数据处理完成")
        except ImportError:
            logger.info("监控模块未找到，跳过消息发送")

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")

        # 发送失败通知（如果有监控系统）
        try:
            from monitor import send_text_message
            send_text_message(f"外贸邦更新数据处理异常中止：{str(e)}")
        except ImportError:
            logger.error("监控模块未找到，无法发送错误通知")

        raise


if __name__ == '__main__':
    main()