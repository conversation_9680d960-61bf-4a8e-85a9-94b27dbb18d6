"""
File Name: wmb_update_process.py
Model:
Description: 基于更新时间取数据的处理器，支持时间判断和分批处理
Author:
Date: 2025-09-03
"""
import os
import multiprocessing as mp
from datetime import datetime, timedelta
from typing import Iterator, Tuple, List, Dict, Any
from wmb_data_process import WMB
from setting import BATCH_SIZE, logger


class UpdateTimeProcessor(WMB):
    """基于更新时间的数据处理器"""

    def __init__(self, max_workers: int = None):
        super().__init__()
        self.current_time = datetime.now()

        # 多进程配置
        self.max_workers = max_workers or min(mp.cpu_count(), 8)  # 默认最多8个进程
        logger.info(f"初始化多进程处理器，进程数: {self.max_workers}")

    def get_target_date(self) -> str:
        """
        根据当前执行时间判断取数据的日期
        12点前取昨天，12点后取当天
        """
        if self.current_time.hour < 12:
            # 12点前取昨天的数据
            target_date = (self.current_time - timedelta(days=1)).strftime('%Y-%m-%d')
            logger.info(f"执行时间: {self.current_time.strftime('%H:%M:%S')}, 取昨天数据: {target_date}")
        else:
            # 12点后取当天的数据
            target_date = self.current_time.strftime('%Y-%m-%d')
            logger.info(f"执行时间: {self.current_time.strftime('%H:%M:%S')}, 取当天数据: {target_date}")

        return target_date

    def get_update_data_count(self, table_name: str, target_date: str) -> int:
        """
        获取指定日期更新且创建时间≠更新时间的数据总数
        """
        sql = f"""
        SELECT COUNT(*)
        FROM {table_name}
        WHERE DATE(update_time) = '{target_date}'
        AND create_time != update_time
        """
        result = self.db_source.read(sql)
        count = result[0][0] if result and result[0] else 0
        logger.info(f"表 {table_name} 在 {target_date} 的更新数据总数: {count}")
        return count

    def get_update_data_batch_iterator(self, table_name: str, target_date: str,
                                     batch_size: int = BATCH_SIZE) -> Iterator[Tuple[List, int, int]]:
        """
        基于时间范围的分批数据迭代器
        由于ID乱序，使用LIMIT OFFSET方式分批获取数据

        Returns:
            Iterator[Tuple[List, int, int]]: (数据列表, 当前批次, 总批次数)
        """
        # 获取总数据量
        total_count = self.get_update_data_count(table_name, target_date)
        if total_count == 0:
            logger.warning(f"表 {table_name} 在 {target_date} 没有找到需要处理的数据")
            return

        # 计算总批次数
        total_batches = (total_count + batch_size - 1) // batch_size
        logger.info(f"开始分批处理，总数据量: {total_count}, 批次大小: {batch_size}, 总批次: {total_batches}")

        # 分批获取数据
        for batch_num in range(total_batches):
            offset = batch_num * batch_size

            if table_name == self.table_source_company:
                # 公司数据查询
                sql = f"""
                SELECT company_id, name, business, country, address, manager,
                       telephone, fax, email, website
                FROM {table_name}
                WHERE DATE(update_time) = '{target_date}'
                AND create_time != update_time
                ORDER BY update_time
                LIMIT {batch_size} OFFSET {offset}
                """
            elif table_name == self.table_source_trade:
                # 贸易数据查询
                sql = f"""
                SELECT amount, bill_no, buyer, buyer_country, buyer_id_std, buyer_port,
                       container, date, descript, descript_label, hs, id, notify_name,
                       origin_country, qty, qty_unit, seller, seller_country, seller_id_std,
                       seller_port, trans, weight, weight_unit, uusd
                FROM {table_name}
                WHERE DATE(update_time) = '{target_date}'
                AND create_time != update_time
                ORDER BY update_time
                LIMIT {batch_size} OFFSET {offset}
                """
            else:
                raise ValueError(f"不支持的表名: {table_name}")

            logger.info(f"正在获取第 {batch_num + 1}/{total_batches} 批数据，偏移量: {offset}")
            data_raw = self.db_source.read(sql)

            if data_raw:
                logger.info(f"第 {batch_num + 1} 批获取到 {len(data_raw)} 条数据")
                yield data_raw, batch_num + 1, total_batches
            else:
                logger.warning(f"第 {batch_num + 1} 批没有获取到数据")

    def run_company_update_process(self):
        """运行公司数据的更新处理流程（多进程版本）"""
        target_date = self.get_target_date()

        logger.info("=" * 50)
        logger.info("开始处理公司更新数据（多进程）")
        logger.info(f"使用进程数: {self.max_workers}")
        logger.info("=" * 50)

        processed_count = 0
        failed_batches = []

        try:
            # 收集所有批次数据
            batch_tasks = []
            for data_raw, current_batch, total_batches in self.get_update_data_batch_iterator(
                self.table_source_company, target_date
            ):
                batch_tasks.append((data_raw, current_batch, total_batches, target_date))

            if not batch_tasks:
                logger.warning("没有找到需要处理的公司数据")
                return

            logger.info(f"准备使用 {self.max_workers} 个进程处理 {len(batch_tasks)} 个批次")

            # 使用进程池处理
            with mp.Pool(processes=self.max_workers) as pool:
                # 提交所有任务
                results = pool.map(process_company_batch_worker, batch_tasks)

                # 汇总结果
                for result in results:
                    if result['success']:
                        processed_count += result['processed_count']
                        logger.info(f"批次 {result['batch_num']} 处理成功，处理 {result['processed_count']} 条数据")
                    else:
                        failed_batches.append(result['batch_num'])
                        logger.error(f"批次 {result['batch_num']} 处理失败: {result['error']}")

            # 处理结果统计
            if failed_batches:
                logger.error(f"公司数据处理完成，但有 {len(failed_batches)} 个批次失败: {failed_batches}")
                logger.info(f"成功处理 {processed_count} 条数据")
            else:
                logger.info(f"公司数据处理完成！总共处理 {processed_count} 条数据")

        except Exception as e:
            logger.error(f"公司数据处理出错: {str(e)}")
            raise

    def run_trade_update_process(self):
        """运行贸易数据的更新处理流程（多进程版本）"""
        target_date = self.get_target_date()

        logger.info("=" * 50)
        logger.info("开始处理贸易更新数据（多进程）")
        logger.info(f"使用进程数: {self.max_workers}")
        logger.info("=" * 50)

        # 初始化必要的映射数据（复用父类逻辑）
        logger.info("正在加载港口映射和单位映射...")
        mapping_port = self.read_customs_port_map()
        mapping_quantity, mapping_weight = self.read_mapping_unit()
        logger.info("映射数据加载完成")

        processed_count = 0
        failed_batches = []

        try:
            # 收集所有批次数据
            batch_tasks = []
            for data_raw, current_batch, total_batches in self.get_update_data_batch_iterator(
                self.table_source_trade, target_date
            ):
                batch_tasks.append((
                    data_raw, current_batch, total_batches, target_date,
                    mapping_port, mapping_quantity, mapping_weight
                ))

            if not batch_tasks:
                logger.warning("没有找到需要处理的贸易数据")
                return

            logger.info(f"准备使用 {self.max_workers} 个进程处理 {len(batch_tasks)} 个批次")

            # 使用进程池处理
            with mp.Pool(processes=self.max_workers) as pool:
                # 提交所有任务
                results = pool.map(process_trade_batch_worker, batch_tasks)

                # 汇总结果
                for result in results:
                    if result['success']:
                        processed_count += result['processed_count']
                        logger.info(f"批次 {result['batch_num']} 处理成功，处理 {result['processed_count']} 条数据")
                    else:
                        failed_batches.append(result['batch_num'])
                        logger.error(f"批次 {result['batch_num']} 处理失败: {result['error']}")

            # 处理结果统计
            if failed_batches:
                logger.error(f"贸易数据处理完成，但有 {len(failed_batches)} 个批次失败: {failed_batches}")
                logger.info(f"成功处理 {processed_count} 条数据")
            else:
                logger.info(f"贸易数据处理完成！总共处理 {processed_count} 条数据")

        except Exception as e:
            logger.error(f"贸易数据处理出错: {str(e)}")
            raise

    def run(self):
        """运行完整的更新数据处理流程"""
        start_time = datetime.now()
        logger.info("=" * 60)
        logger.info("开始执行基于更新时间的数据处理任务")
        logger.info(f"任务开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

        try:
            # 处理公司数据
            self.run_company_update_process()

            # 处理贸易数据
            self.run_trade_update_process()

            end_time = datetime.now()
            duration = end_time - start_time

            logger.info("=" * 60)
            logger.info("数据处理任务完成！")
            logger.info(f"任务结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"总耗时: {duration}")
            logger.info("=" * 60)

        except Exception as e:
            end_time = datetime.now()
            duration = end_time - start_time

            logger.error("=" * 60)
            logger.error("数据处理任务异常终止！")
            logger.error(f"错误信息: {str(e)}")
            logger.error(f"任务结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.error(f"运行时长: {duration}")
            logger.error("=" * 60)
            raise


def main():
    """主函数"""
    try:
        # 可以通过环境变量指定进程数
        max_workers = int(os.environ.get('MAX_WORKERS', 0)) or None
        processor = UpdateTimeProcessor(max_workers=max_workers)
        processor.run()

        # 发送成功通知（如果有监控系统）
        try:
            from monitor import send_text_message
            send_text_message("外贸邦更新数据处理完成（多进程版本）")
        except ImportError:
            logger.info("监控模块未找到，跳过消息发送")

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")

        # 发送失败通知（如果有监控系统）
        try:
            from monitor import send_text_message
            send_text_message(f"外贸邦更新数据处理异常中止：{str(e)}")
        except ImportError:
            logger.error("监控模块未找到，无法发送错误通知")

        raise


# ==================== 多进程工作函数 ====================

def process_company_batch_worker(batch_data: Tuple[List, int, int, str]) -> Dict[str, Any]:
    """
    多进程工作函数：处理单个批次的公司数据

    Args:
        batch_data: (数据列表, 当前批次, 总批次数, 目标日期)

    Returns:
        Dict: 处理结果统计
    """
    data_raw, current_batch, total_batches, target_date = batch_data

    try:
        # 创建新的处理器实例（每个进程独立）
        processor = UpdateTimeProcessor()

        logger.info(f"进程 {os.getpid()} 开始处理公司数据批次 {current_batch}/{total_batches}")

        # 处理数据
        processor.processing_company(data_raw)

        result = {
            'success': True,
            'batch_num': current_batch,
            'processed_count': len(data_raw),
            'error': None,
            'process_id': os.getpid()
        }

        logger.info(f"进程 {os.getpid()} 完成公司数据批次 {current_batch}，处理 {len(data_raw)} 条")
        return result

    except Exception as e:
        error_msg = f"进程 {os.getpid()} 处理公司数据批次 {current_batch} 失败: {str(e)}"
        logger.error(error_msg)

        return {
            'success': False,
            'batch_num': current_batch,
            'processed_count': 0,
            'error': error_msg,
            'process_id': os.getpid()
        }


def process_trade_batch_worker(batch_data: Tuple[List, int, int, str, Dict, Dict, Dict]) -> Dict[str, Any]:
    """
    多进程工作函数：处理单个批次的贸易数据

    Args:
        batch_data: (数据列表, 当前批次, 总批次数, 目标日期, 港口映射, 数量单位映射, 重量单位映射)

    Returns:
        Dict: 处理结果统计
    """
    data_raw, current_batch, total_batches, target_date, mapping_port, mapping_quantity, mapping_weight = batch_data

    try:
        # 创建新的处理器实例（每个进程独立）
        processor = UpdateTimeProcessor()

        # 设置映射数据
        processor.mapping_port = mapping_port
        processor.mapping_quantity = mapping_quantity
        processor.mapping_weight = mapping_weight

        logger.info(f"进程 {os.getpid()} 开始处理贸易数据批次 {current_batch}/{total_batches}")

        # 处理数据
        processor.processing_trade(data_raw)

        result = {
            'success': True,
            'batch_num': current_batch,
            'processed_count': len(data_raw),
            'error': None,
            'process_id': os.getpid()
        }

        logger.info(f"进程 {os.getpid()} 完成贸易数据批次 {current_batch}，处理 {len(data_raw)} 条")
        return result

    except Exception as e:
        error_msg = f"进程 {os.getpid()} 处理贸易数据批次 {current_batch} 失败: {str(e)}"
        logger.error(error_msg)

        return {
            'success': False,
            'batch_num': current_batch,
            'processed_count': 0,
            'error': error_msg,
            'process_id': os.getpid()
        }


if __name__ == '__main__':
    main()