import json
import requests


def send_text_message(content):
    # url = 'https://open.feishu.cn/open-apis/bot/v2/hook/62b68321-d4b2-44f8-bc96-3a24b107f086'
    # 个人版测试
    url = 'https://open.feishu.cn/open-apis/bot/v2/hook/1b336a7d-9eec-444e-a7b1-fd1e883d9b33'
    headers = {
        "Content-Type": "application/json; charset=utf-8",
    }
    payload_message = {
        "msg_type": "text",
        "content": {
            # @ 单个用户 <at user_id="ou_xxx">名字</at>
            # "text": "流量监控\n"+content + "<at user_id=\"bf888888\">陈欢</at>"
            # @ 所有人 <at user_id="all">所有人</at>
            "text": content
        }
    }
    response = requests.post(url=url, data=json.dumps(payload_message), headers=headers, timeout=10)
    return response.json


if __name__ == '__main__':
    send_text_message('测试监控接口')
