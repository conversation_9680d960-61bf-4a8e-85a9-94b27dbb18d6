# UpdateTimeProcessor 使用说明

## 概述

`UpdateTimeProcessor` 是一个基于更新时间取数据的处理器，专门用于处理创建时间不等于更新时间的数据。它继承自 `WMB` 类，复用了现有的数据清洗和处理逻辑。

## 主要特性

### 1. 智能时间判断 🕐
- **12点前执行**：自动取昨天的数据
- **12点后执行**：自动取当天的数据
- 自动记录执行时间和目标日期

### 2. 分批处理机制 📦
- 使用 `LIMIT OFFSET` 方式分批获取数据（解决ID乱序问题）
- 默认批次大小：50,000条（可配置）
- 内存占用可控，支持大数据量处理
- 实时进度监控和日志记录

### 3. 数据过滤条件 🔍
- 只处理 `DATE(update_time) = target_date` 的数据
- 只处理 `create_time != update_time` 的数据
- 按 `update_time` 排序确保数据一致性

### 4. 完整的错误处理 🛡️
- 异常捕获和日志记录
- 进度跟踪和恢复机制
- 监控系统集成（可选）

## 使用方法

### 基本使用

```python
from wmb_update_process import UpdateTimeProcessor

# 创建处理器实例
processor = UpdateTimeProcessor()

# 运行完整处理流程
processor.run()
```

### 单独处理公司数据

```python
processor = UpdateTimeProcessor()
processor.run_company_update_process()
```

### 单独处理贸易数据

```python
processor = UpdateTimeProcessor()
processor.run_trade_update_process()
```

### 自定义时间测试

```python
from datetime import datetime

processor = UpdateTimeProcessor()
# 设置测试时间
processor.current_time = datetime(2025, 9, 3, 8, 30)  # 8:30
target_date = processor.get_target_date()  # 会取昨天的数据
```

## 命令行运行

```bash
# 直接运行
python wmb_update_process.py

# 后台运行并记录日志
nohup python wmb_update_process.py > ./logs/wmb_update_process.log 2>&1 &

# 查看日志
tail -f ./logs/wmb_update_process.log
```

## 配置说明

### 数据库配置
处理器使用 `setting.py` 中的数据库配置：
- `MYSQL_INFO_SOURCE`：源数据库配置
- `MYSQL_INFO_TARGET`：目标数据库配置

### 批次大小配置
在 `setting.py` 中修改 `BATCH_SIZE` 来调整批次大小：
```python
BATCH_SIZE = 50000  # 默认50,000条
```

## 日志输出示例

```
============================================================
开始执行基于更新时间的数据处理任务
任务开始时间: 2025-09-03 10:39:42
============================================================
执行时间: 10:39:42, 取昨天数据: 2025-09-02
==================================================
开始处理公司更新数据
==================================================
表 company 在 2025-09-02 的更新数据总数: 2
开始分批处理，总数据量: 2, 批次大小: 50000, 总批次: 1
正在获取第 1/1 批数据，偏移量: 0
第 1 批获取到 2 条数据
处理公司数据 - 批次 1/1
批次 1 处理完成，本批处理 2 条，累计处理 2 条
公司数据处理完成！总共处理 2 条数据
```

## 与原有系统的区别

| 特性 | 原有 wmb_data_process.py | 新的 UpdateTimeProcessor |
|------|-------------------------|-------------------------|
| 数据获取方式 | 基于ID范围 (id between start and end) | 基于更新时间 (DATE(update_time) = target_date) |
| 时间判断 | 无 | 智能判断（12点前/后） |
| 数据过滤 | 无特殊过滤 | 只取 create_time != update_time |
| 分批策略 | ID连续分批 | LIMIT OFFSET分批（适应ID乱序） |
| 进度记录 | 更新记录表 | 实时日志监控 |

## 测试

运行测试脚本验证功能：

```bash
python test_update_processor.py
```

测试内容包括：
- 时间判断逻辑验证
- 数据库连接测试
- 分批迭代器测试

## 注意事项

1. **数据库权限**：确保有读取源数据库和写入目标数据库的权限
2. **时间字段**：确保源表有 `create_time` 和 `update_time` 字段
3. **网络稳定性**：长时间运行时注意网络连接稳定性
4. **监控集成**：如果有监控系统，会自动发送处理完成/失败通知

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `setting.py` 中的数据库配置
   - 确认网络连接和权限

2. **没有找到数据**
   - 检查目标日期是否有更新数据
   - 确认 `create_time != update_time` 的条件

3. **内存占用过高**
   - 减小 `BATCH_SIZE` 的值
   - 检查数据处理逻辑是否有内存泄漏

## 性能优化建议

1. **索引优化**：在 `update_time` 和 `create_time` 字段上建立索引
2. **批次调优**：根据服务器性能调整 `BATCH_SIZE`
3. **并发处理**：可以考虑公司数据和贸易数据并行处理
4. **定时任务**：建议设置为定时任务，在数据更新后自动执行
